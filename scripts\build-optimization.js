/**
 * 构建优化脚本
 * 用于清理缓存和优化构建环境
 */
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 开始构建优化...');

// 1. 清理缓存
console.log('📦 清理构建缓存...');
try {
  execSync('rimraf node_modules/.vite', { stdio: 'inherit' });
  execSync('rimraf node_modules/.cache', { stdio: 'inherit' });
  execSync('rimraf dist', { stdio: 'inherit' });
  console.log('✅ 缓存清理完成');
} catch (error) {
  console.log('⚠️ 缓存清理失败，继续构建...');
}

// 2. 检查内存设置
console.log('🔧 检查Node.js内存设置...');
const nodeOptions = process.env.NODE_OPTIONS || '';
if (!nodeOptions.includes('--max-old-space-size')) {
  console.log('⚠️ 建议设置 NODE_OPTIONS=--max-old-space-size=8192');
}

// 3. 分析包大小（可选）
const shouldAnalyze = process.argv.includes('--analyze');
if (shouldAnalyze) {
  console.log('📊 启用包大小分析...');
  process.env.REPORT = 'true';
}

// 4. 设置构建环境变量
process.env.NODE_ENV = 'production';
process.env.VITE_USE_IMAGEMIN = 'false'; // 关闭图片压缩以提升速度

console.log('🏗️ 开始构建...');
try {
  execSync('vite build --mode dev', { stdio: 'inherit' });
  console.log('✅ 构建完成！');
} catch (error) {
  console.error('❌ 构建失败:', error.message);
  process.exit(1);
}

console.log('🎉 构建优化完成！');
