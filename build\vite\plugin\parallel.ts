/**
 * 并行构建插件 - 优化构建性能
 */
import type { PluginOption } from 'vite';
import { cpus } from 'os';

export function configParallelPlugin(): PluginOption {
  return {
    name: 'vite-plugin-parallel',
    config(config) {
      // 设置并行处理数量为CPU核心数
      const cpuCount = cpus().length;
      
      if (config.build) {
        config.build.rollupOptions = config.build.rollupOptions || {};
        config.build.rollupOptions.maxParallelFileOps = Math.min(cpuCount, 8);
      }
      
      // 优化esbuild并行处理
      if (config.esbuild) {
        config.esbuild.target = 'es2020';
      }
    },
  };
}
