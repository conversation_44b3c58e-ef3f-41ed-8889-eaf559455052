/**
 * 超快速构建脚本 - 针对20-30分钟构建时间的激进优化
 */
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('⚡ 启动超快速构建模式...');

// 1. 激进的缓存清理
console.log('🧹 清理所有缓存...');
try {
  execSync('rimraf node_modules/.vite', { stdio: 'inherit' });
  execSync('rimraf node_modules/.cache', { stdio: 'inherit' });
  execSync('rimraf dist', { stdio: 'inherit' });
  execSync('rimraf .eslintcache', { stdio: 'inherit' });
  console.log('✅ 缓存清理完成');
} catch (error) {
  console.log('⚠️ 缓存清理失败，继续构建...');
}

// 2. 设置激进的环境变量
console.log('⚙️ 设置超快速构建环境...');
process.env.NODE_ENV = 'production';
process.env.VITE_USE_IMAGEMIN = 'false';
process.env.VITE_BUILD_COMPRESS = 'none';
process.env.VITE_USE_MOCK = 'false';
process.env.VITE_LEGACY = 'false';
process.env.NODE_OPTIONS = '--max-old-space-size=8192 --no-warnings';

// 3. 临时修改TypeScript配置以提升编译速度
const tsConfigPath = path.resolve(process.cwd(), 'tsconfig.json');
let originalTsConfig = null;

try {
  originalTsConfig = fs.readFileSync(tsConfigPath, 'utf8');
  const tsConfig = JSON.parse(originalTsConfig);
  
  // 优化TypeScript编译选项
  tsConfig.compilerOptions = {
    ...tsConfig.compilerOptions,
    skipLibCheck: true,
    skipDefaultLibCheck: true,
    incremental: true,
    tsBuildInfoFile: '.tsbuildinfo'
  };
  
  fs.writeFileSync(tsConfigPath, JSON.stringify(tsConfig, null, 2));
  console.log('✅ TypeScript配置已优化');
} catch (error) {
  console.log('⚠️ TypeScript配置优化失败，使用默认配置');
}

// 4. 创建临时的超快速Vite配置
const tempConfigPath = path.resolve(process.cwd(), 'vite.config.ultra-fast.ts');
const ultraFastConfig = `
import type { UserConfig, ConfigEnv } from 'vite';
import { loadEnv } from 'vite';
import { resolve } from 'path';
import { OUTPUT_DIR } from './build/constant';
import vue from '@vitejs/plugin-vue';
import vueJsx from '@vitejs/plugin-vue-jsx';

function pathResolve(dir: string) {
  return resolve(process.cwd(), '.', dir);
}

export default ({ command, mode }: ConfigEnv): UserConfig => {
  const root = process.cwd();
  const env = loadEnv(mode, root);
  const isBuild = command === 'build';

  return {
    base: '/',
    root,
    resolve: {
      alias: [
        { find: /\\/@\\//, replacement: pathResolve('src') + '/' },
        { find: /\\/#\\//, replacement: pathResolve('types') + '/' },
        { find: /@\\//, replacement: pathResolve('src') + '/' },
        { find: /#\\//, replacement: pathResolve('types') + '/' },
      ],
    },
    build: {
      minify: 'esbuild',
      target: 'es2022',
      outDir: OUTPUT_DIR,
      rollupOptions: {
        external: ['mars3d-cesium', 'tinymce', 'echarts'],
        output: {
          manualChunks: {
            'vue-vendor': ['vue', 'vue-router', 'pinia'],
            'antd-vendor': ['ant-design-vue'],
            'utils-vendor': ['lodash-es', 'axios']
          }
        },
        maxParallelFileOps: 20
      },
      reportCompressedSize: false,
      chunkSizeWarningLimit: 5000,
      sourcemap: false
    },
    esbuild: {
      drop: ['console', 'debugger'],
      legalComments: 'none',
      target: 'es2022',
      minifyIdentifiers: true,
      minifySyntax: true,
      minifyWhitespace: true,
      keepNames: false,
      treeShaking: true
    },
    plugins: [vue(), vueJsx()],
    optimizeDeps: {
      esbuildOptions: { target: 'es2022' },
      exclude: ['@jeecg/online', 'mars3d-cesium', 'tinymce', 'echarts'],
      include: ['vue', 'vue-router', 'pinia', 'ant-design-vue', 'axios']
    }
  };
};
`;

try {
  fs.writeFileSync(tempConfigPath, ultraFastConfig);
  console.log('✅ 超快速配置文件已创建');
} catch (error) {
  console.log('⚠️ 无法创建超快速配置，使用默认配置');
}

// 5. 执行超快速构建
console.log('🚀 开始超快速构建...');
const startTime = Date.now();

try {
  const configFlag = fs.existsSync(tempConfigPath) ? `--config ${tempConfigPath}` : '';
  execSync(`vite build ${configFlag} --mode dev`, { stdio: 'inherit' });
  
  const endTime = Date.now();
  const duration = Math.round((endTime - startTime) / 1000);
  console.log(`✅ 构建完成！耗时: ${duration}秒`);
  
  if (duration < 300) { // 5分钟以内
    console.log('🎉 构建速度已优化到5分钟以内！');
  } else {
    console.log('⚠️ 构建时间仍然较长，可能需要进一步优化');
  }
  
} catch (error) {
  console.error('❌ 构建失败:', error.message);
} finally {
  // 6. 清理临时文件
  try {
    if (fs.existsSync(tempConfigPath)) {
      fs.unlinkSync(tempConfigPath);
    }
    if (originalTsConfig) {
      fs.writeFileSync(tsConfigPath, originalTsConfig);
    }
    console.log('🧹 临时文件已清理');
  } catch (error) {
    console.log('⚠️ 临时文件清理失败');
  }
}

console.log('⚡ 超快速构建完成！');
