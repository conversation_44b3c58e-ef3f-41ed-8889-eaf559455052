import type { UserConfig, ConfigEnv } from 'vite';
import pkg from './package.json';
import dayjs from 'dayjs';
import { loadEnv } from 'vite';
import { resolve } from 'path';
import { generateModifyVars } from './build/generate/generateModifyVars';
import { createProxy } from './build/vite/proxy';
import { wrapperEnv } from './build/utils';
import { createVitePlugins } from './build/vite/plugin';
import { OUTPUT_DIR } from './build/constant';
import path from 'path';
import { mars3dPlugin } from 'vite-plugin-mars3d';

function pathResolve(dir: string) {
  return resolve(process.cwd(), '.', dir);
}

const { dependencies, devDependencies, name, version } = pkg;
const __APP_INFO__ = {
  pkg: { dependencies, devDependencies, name, version },
  lastBuildTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
};

export default ({ command, mode }: ConfigEnv): UserConfig => {
  const root = process.cwd();

  const env = loadEnv(mode, root);

  // The boolean type read by loadEnv is a string. This function can be converted to boolean type
  const viteEnv = wrapperEnv(env);

  const { VITE_PORT, VITE_PUBLIC_PATH, VITE_PROXY } = viteEnv;

  const isBuild = command === 'build';
  //console.log("VITE_PROXY===", VITE_PROXY)
  return {
    base: VITE_PUBLIC_PATH,
    root,
    resolve: {
      alias: [
        {
          find: 'vue-i18n',
          replacement: 'vue-i18n/dist/vue-i18n.cjs.js',
        },
        // /@/xxxx => src/xxxx
        {
          find: /\/@\//,
          replacement: pathResolve('src') + '/',
        },
        // /#/xxxx => types/xxxx
        {
          find: /\/#\//,
          replacement: pathResolve('types') + '/',
        },
        {
          find: /@\//,
          replacement: pathResolve('src') + '/',
        },
        // /#/xxxx => types/xxxx
        {
          find: /#\//,
          replacement: pathResolve('types') + '/',
        },
        {
          find: '@mars',
          replacement: path.join(__dirname, 'src'),
        },
      ],
    },
    server: {
      // Listening on all local IPs
      host: true,
      https: false,
      port: VITE_PORT,
      // Load proxy configuration from .env
      proxy: createProxy(VITE_PROXY),
    },
    build: {
      minify: 'esbuild',
      target: 'es2015',
      cssTarget: 'chrome80',
      outDir: OUTPUT_DIR,
      // 启用并行构建
      rollupOptions: {
        // 外部化大型依赖，减少打包体积
        external: isBuild ? [] : ['mars3d-cesium'],
        output: {
          chunkFileNames: 'js/[name]-[hash].js',
          entryFileNames: 'js/[name]-[hash].js',
          assetFileNames: 'assets/[name]-[hash].[ext]',
          // 优化的代码分割策略
          manualChunks: (id) => {
            // 将node_modules中的包分组
            if (id.includes('node_modules')) {
              // 大型UI库单独分包
              if (id.includes('ant-design-vue')) return 'antd-vue';
              if (id.includes('echarts')) return 'echarts';
              if (id.includes('tinymce')) return 'tinymce';
              if (id.includes('codemirror')) return 'codemirror';
              if (id.includes('vxe-table')) return 'vxe-table';
              if (id.includes('@jeecg/online')) return 'jeecg-online';

              // Mars3D相关
              if (id.includes('mars3d')) return 'mars3d';
              if (id.includes('cesium')) return 'cesium';

              // Vue生态
              if (id.includes('vue') || id.includes('pinia') || id.includes('@vue')) return 'vue-vendor';

              // 工具库
              if (id.includes('lodash') || id.includes('dayjs') || id.includes('axios')) return 'utils';

              // 其他第三方库
              return 'vendor';
            }
          },
        },
        // 增加并行处理
        maxParallelFileOps: 5,
      },
      // 关闭压缩大小报告以提升速度
      reportCompressedSize: false,
      // 提高警告阈值
      chunkSizeWarningLimit: 3000,
      // 启用并行构建
      commonjsOptions: {
        include: /node_modules|packages/,
      },
    },
    esbuild: {
      // 清除全局的console.log和debug
      drop: isBuild ? ['console', 'debugger'] : [],
      legalComments: 'none', // 移除所有注释
      charset: 'utf8', // 设置字符集为 utf8
      treeShaking: true, // 开启 Tree Shaking
      // 生产环境关闭sourcemap以提升构建速度
      sourcemap: !isBuild,
      // 启用并行处理
      target: 'es2020',
      // 优化构建性能
      minifyIdentifiers: isBuild,
      minifySyntax: isBuild,
      minifyWhitespace: isBuild,
    },
    define: {
      // setting vue-i18-next
      // Suppress warning
      __INTLIFY_PROD_DEVTOOLS__: false,
      __APP_INFO__: JSON.stringify(__APP_INFO__),
    },
    css: {
      preprocessorOptions: {
        less: {
          modifyVars: generateModifyVars(),
          javascriptEnabled: true,
        },
      },
    },

    // The vite plugin used by the project. The quantity is large, so it is separately extracted and managed
    plugins: [createVitePlugins(viteEnv, isBuild), mars3dPlugin({ useStatic: false })],
    // 优化的预加载构建配置
    optimizeDeps: {
      esbuildOptions: {
        target: 'es2020',
        // 启用并行处理
        plugins: [],
      },
      exclude: [
        // 升级vite4后，需要排除online依赖
        '@jeecg/online',
        'decoder.js',
        'decoder.wasm',
        // 排除大型依赖以减少预构建时间
        'mars3d-cesium',
        'tinymce',
        'echarts',
      ],
      // 精简的预加载配置，只包含核心依赖
      include: [
        // Vue核心
        'vue',
        'vue-router',
        'pinia',
        '@vue/runtime-core',
        '@vue/shared',

        // UI库核心
        'ant-design-vue',
        '@ant-design/icons-vue',

        // 工具库
        'axios',
        'lodash-es',
        'dayjs',
        'qs',

        // 其他核心依赖
        '@vueuse/core',
        'nprogress',
      ],
      // 强制预构建某些依赖
      force: false,
    },
  };
};
