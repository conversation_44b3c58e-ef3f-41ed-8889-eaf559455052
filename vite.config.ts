import type { UserConfig, ConfigEnv } from 'vite';
import pkg from './package.json';
import dayjs from 'dayjs';
import { loadEnv } from 'vite';
import { resolve } from 'path';
import { generateModifyVars } from './build/generate/generateModifyVars';
import { createProxy } from './build/vite/proxy';
import { wrapperEnv } from './build/utils';
import { createVitePlugins } from './build/vite/plugin';
import { OUTPUT_DIR } from './build/constant';
import path from 'path';
import { mars3dPlugin } from 'vite-plugin-mars3d';

function pathResolve(dir: string) {
  return resolve(process.cwd(), '.', dir);
}

const { dependencies, devDependencies, name, version } = pkg;
const __APP_INFO__ = {
  pkg: { dependencies, devDependencies, name, version },
  lastBuildTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
};

export default ({ command, mode }: ConfigEnv): UserConfig => {
  const root = process.cwd();

  const env = loadEnv(mode, root);

  // The boolean type read by loadEnv is a string. This function can be converted to boolean type
  const viteEnv = wrapperEnv(env);

  const { VITE_PORT, VITE_PUBLIC_PATH, VITE_PROXY } = viteEnv;

  const isBuild = command === 'build';
  //console.log("VITE_PROXY===", VITE_PROXY)
  return {
    base: VITE_PUBLIC_PATH,
    root,
    resolve: {
      alias: [
        {
          find: 'vue-i18n',
          replacement: 'vue-i18n/dist/vue-i18n.cjs.js',
        },
        // /@/xxxx => src/xxxx
        {
          find: /\/@\//,
          replacement: pathResolve('src') + '/',
        },
        // /#/xxxx => types/xxxx
        {
          find: /\/#\//,
          replacement: pathResolve('types') + '/',
        },
        {
          find: /@\//,
          replacement: pathResolve('src') + '/',
        },
        // /#/xxxx => types/xxxx
        {
          find: /#\//,
          replacement: pathResolve('types') + '/',
        },
        {
          find: '@mars',
          replacement: path.join(__dirname, 'src'),
        },
      ],
    },
    server: {
      // Listening on all local IPs
      host: true,
      https: false,
      port: VITE_PORT,
      // Load proxy configuration from .env
      proxy: createProxy(VITE_PROXY),
    },
    build: {
      minify: 'esbuild',
      target: 'es2015',
      cssTarget: 'chrome80',
      outDir: OUTPUT_DIR,
      // 启用并行构建
      rollupOptions: {
        // 激进的外部化策略 - 将大型依赖外部化
        external: isBuild ? ['mars3d-cesium', 'tinymce', 'echarts', 'codemirror'] : ['mars3d-cesium'],
        output: {
          chunkFileNames: 'js/[name]-[hash].js',
          entryFileNames: 'js/[name]-[hash].js',
          assetFileNames: 'assets/[name]-[hash].[ext]',
          // 简化的代码分割策略
          manualChunks: {
            // 核心框架
            'vue-vendor': ['vue', 'vue-router', 'pinia'],
            // UI库
            'antd-vendor': ['ant-design-vue', '@ant-design/icons-vue'],
            // 工具库
            'utils-vendor': ['lodash-es', 'dayjs', 'axios', 'qs'],
            // 其他大型库
            'other-vendor': ['@vueuse/core', '@jeecg/online'],
          },
          // 外部化的全局变量映射
          globals: {
            'mars3d-cesium': 'Cesium',
            tinymce: 'tinymce',
            echarts: 'echarts',
            codemirror: 'CodeMirror',
          },
        },
        // 最大并行处理
        maxParallelFileOps: 10,
        // 禁用一些耗时的优化
        treeshake: {
          preset: 'smallest',
          manualPureFunctions: ['console.log'],
        },
      },
      // 关闭压缩大小报告以提升速度
      reportCompressedSize: false,
      // 提高警告阈值
      chunkSizeWarningLimit: 3000,
      // 启用并行构建
      commonjsOptions: {
        include: /node_modules|packages/,
      },
    },
    esbuild: {
      // 清除全局的console.log和debug
      drop: isBuild ? ['console', 'debugger'] : [],
      legalComments: 'none', // 移除所有注释
      charset: 'utf8', // 设置字符集为 utf8
      treeShaking: true, // 开启 Tree Shaking
      // 完全关闭sourcemap以提升构建速度
      sourcemap: false,
      // 使用更新的目标以提升性能
      target: 'es2022',
      // 激进的压缩设置
      minifyIdentifiers: true,
      minifySyntax: true,
      minifyWhitespace: true,
      // 关闭一些耗时的优化
      keepNames: false,
      // 启用更快的转换
      platform: 'browser',
      format: 'esm',
    },
    define: {
      // setting vue-i18-next
      // Suppress warning
      __INTLIFY_PROD_DEVTOOLS__: false,
      __APP_INFO__: JSON.stringify(__APP_INFO__),
    },
    css: {
      preprocessorOptions: {
        less: {
          modifyVars: generateModifyVars(),
          javascriptEnabled: true,
        },
      },
    },

    // The vite plugin used by the project. The quantity is large, so it is separately extracted and managed
    plugins: [createVitePlugins(viteEnv, isBuild), mars3dPlugin({ useStatic: false })],
    // 优化的预加载构建配置
    optimizeDeps: {
      esbuildOptions: {
        target: 'es2020',
        // 启用并行处理
        plugins: [],
      },
      exclude: [
        // 升级vite4后，需要排除online依赖
        '@jeecg/online',
        'decoder.js',
        'decoder.wasm',
        // 排除大型依赖以减少预构建时间
        'mars3d-cesium',
        'tinymce',
        'echarts',
      ],
      // 精简的预加载配置，只包含核心依赖
      include: [
        // Vue核心
        'vue',
        'vue-router',
        'pinia',
        '@vue/runtime-core',
        '@vue/shared',

        // UI库核心
        'ant-design-vue',
        '@ant-design/icons-vue',

        // 工具库
        'axios',
        'lodash-es',
        'dayjs',
        'qs',

        // 其他核心依赖
        '@vueuse/core',
        'nprogress',
      ],
      // 强制预构建某些依赖
      force: false,
    },
  };
};
