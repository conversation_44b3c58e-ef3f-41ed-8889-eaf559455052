{"name": "jeecgboot-vue3", "version": "3.5.4", "author": {"name": "jeecg", "email": "jeec<PERSON>@163.com", "url": "https://github.com/jeecgboot/jeecgboot-vue3"}, "scripts": {"pinstall": "pnpm install", "clean:cache": "rimraf node_modules/.cache/ && rimraf node_modules/.vite", "dev": "vite --mode dev --open", "dev:sit": "vite --mode sit", "dev:prod": "vite --mode prod", "build": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=8192 vite build --mode dev && esno ./build/script/postBuild.ts", "build:fast": "node scripts/build-optimization.js", "build:analyze": "node scripts/build-optimization.js --analyze", "preview": "npm run build && vite preview", "reinstall": "rimraf pnpm-lock.yaml && rimraf yarn.lock && rimraf package.lock.json && rimraf node_modules && npm run install", "clean:lib": "rimraf node_modules", "gen:icon": "esno ./build/generate/icon/index.ts", "batch:prettier": "prettier --write  \"src/**/*.{js,json,tsx,css,less,scss,vue,html,md}\"", "upgrade:log": "conventional-changelog -p angular -i CHANGELOG.md -s", "husky:install": "husky install"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@ant-design/colors": "^7.0.0", "@ant-design/icons-vue": "^6.1.0", "@icon-park/svg": "^1.4.2", "@iconify/iconify": "^3.1.1", "@jeecg/online": "3.5.3-vite4", "@liveqing/liveplayer-v3": "3.7.19", "@logicflow/core": "^1.2.10", "@logicflow/extension": "^1.2.10", "@qiaoqiaoyun/drag-free": "^1.1.3", "@turf/turf": "^6.5.0", "@vue/runtime-core": "^3.3.4", "@vue/shared": "^3.3.4", "@vueuse/core": "^10.2.1", "@vueuse/shared": "^10.2.1", "@zxcvbn-ts/core": "^3.0.3", "ailabel": "4.1.3", "ant-design-vue": "^3.2.20", "axios": "^1.5.0", "china-area-data": "^5.0.1", "clipboard": "^2.0.11", "codemirror": "^5.65.3", "coordtransform": "^2.1.2", "cron-parser": "^4.9.0", "cropperjs": "^1.5.13", "crypto-js": "^4.1.1", "dayjs": "^1.11.9", "dom-align": "^1.12.4", "echarts": "^5.4.3", "emoji-mart-vue-fast": "^15.0.0", "enquire.js": "^2.1.6", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.2", "intro.js": "^7.0.1", "js-audio-recorder": "^1.0.7", "jszip": "^3.10.1", "jszip-utils": "^0.1.0", "kml-geojson": "^1.2.2", "lodash-es": "^4.17.21", "lodash.get": "^4.4.2", "mars3d": "file:packages/mars3d", "mars3d-cesium": "^1.122.0", "md5": "^2.3.0", "mockjs": "^1.1.0", "nipplejs": "^0.10.1", "nprogress": "^0.2.0", "pannellum": "^2.5.6", "path-to-regexp": "^6.2.1", "pinia": "2.1.6", "print-js": "^1.6.0", "qrcode": "^1.5.3", "qrcodejs2": "0.0.2", "qs": "^6.11.2", "resize-observer-polyfill": "^1.5.1", "rollup-plugin-copy": "3.4.0", "showdown": "^2.1.0", "sortablejs": "^1.15.0", "spark-md5": "^3.0.2", "tiff.js": "^1.0.0", "tinymce": "^5.10.3", "vditor": "^3.9.5", "vue": "3.3.4", "vue-color-kit": "^1.0.6", "vue-cropper": "^0.6.2", "vue-cropperjs": "^5.0.0", "vue-i18n": "^9.2.2", "vue-infinite-scroll": "^2.0.2", "vue-json-pretty": "^2.2.4", "vue-print-nb-jeecg": "^1.0.12", "vue-router": "^4.2.4", "vue-types": "^5.1.1", "vuedraggable": "^4.1.0", "vxe-table": "4.1.0", "vxe-table-plugin-antd": "3.0.5", "xe-utils": "^3.3.1", "xss": "^1.0.14"}, "devDependencies": {"@commitlint/cli": "^17.7.1", "@commitlint/config-conventional": "^17.7.0", "@iconify/json": "^2.2.107", "@purge-icons/generated": "^0.9.0", "@rys-fe/vite-plugin-theme": "^0.8.6", "@types/codemirror": "^5.60.8", "@types/crypto-js": "^4.1.1", "@types/fs-extra": "^11.0.1", "@types/inquirer": "^9.0.3", "@types/intro.js": "^5.1.1", "@types/jest": "^29.5.4", "@types/lodash-es": "^4.17.8", "@types/mockjs": "^1.0.7", "@types/node": "^20.5.6", "@types/nprogress": "^0.2.0", "@types/pinyin": "^2.10.0", "@types/qrcode": "^1.5.1", "@types/qs": "^6.9.7", "@types/showdown": "^2.0.1", "@types/sortablejs": "^1.15.1", "@typescript-eslint/eslint-plugin": "^6.4.1", "@typescript-eslint/parser": "^6.5.0", "@vitejs/plugin-legacy": "^4.1.1", "@vitejs/plugin-vue": "^4.3.3", "@vitejs/plugin-vue-jsx": "^3.0.2", "@vue/compiler-sfc": "^3.3.4", "@vue/test-utils": "^2.4.1", "agora-rtc-sdk-ng": "latest", "autoprefixer": "^10.4.15", "commitizen": "^4.3.0", "conventional-changelog-cli": "^3.0.0", "cross-env": "^7.0.3", "cz-git": "^1.7.1", "czg": "^1.7.1", "dotenv": "^16.3.1", "eslint": "^8.47.0", "eslint-config-prettier": "^9.0.0", "eslint-define-config": "^1.23.0", "eslint-plugin-jest": "^27.2.3", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-vue": "^9.17.0", "esno": "^0.17.0", "fs-extra": "^11.1.1", "http-server": "^14.1.1", "husky": "^8.0.3", "inquirer": "^9.2.10", "is-ci": "^3.0.1", "jest": "^29.6.4", "less": "^4.2.0", "lint-staged": "14.0.1", "npm-run-all": "^4.1.5", "picocolors": "^1.0.0", "postcss": "^8.4.28", "postcss-html": "^1.5.0", "postcss-less": "^6.0.0", "prettier": "^3.0.2", "pretty-quick": "^3.1.3", "rimraf": "^5.0.1", "rollup": "^3.28.1", "rollup-plugin-visualizer": "^5.9.2", "stylelint": "^15.10.3", "stylelint-config-prettier": "^9.0.5", "stylelint-config-recommended": "^13.0.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^34.0.0", "stylelint-order": "^6.0.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.1.6", "unocss": "^0.55.3", "vite": "^4.4.4", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "^3.2.0", "vite-plugin-imagemin": "^0.6.1", "vite-plugin-mars3d": "^3.1.3", "vite-plugin-mkcert": "^1.16.0", "vite-plugin-mock": "^2.9.6", "vite-plugin-purge-icons": "^0.9.2", "vite-plugin-pwa": "^0.16.4", "vite-plugin-style-import": "^2.0.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-setup-extend-plus": "^0.1.0", "vue-eslint-parser": "^9.3.1", "vue-tsc": "^1.8.8"}, "resolutions": {"bin-wrapper": "npm:bin-wrapper-china", "rollup": "^3.26.3"}, "repository": {"type": "git", "url": "git+https://github.com/jeecgboot/jeecgboot-vue3.git"}, "license": "MIT", "bugs": {"url": "https://github.com/jeecgboot/jeecgboot-vue3/issues"}, "homepage": "https://github.com/jeecgboot/jeecgboot-vue3", "engines": {"node": "^12 || >=14"}}