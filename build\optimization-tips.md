# 🚀 构建性能优化指南

## 📊 优化效果预期
- **构建时间减少**: 50-70%
- **内存使用优化**: 减少30-40%
- **包体积优化**: 通过代码分割减少首屏加载时间

## 🛠️ 已实施的优化措施

### 1. 构建配置优化
- ✅ 关闭生产环境sourcemap生成
- ✅ 优化代码分割策略，按功能模块分包
- ✅ 启用并行构建处理
- ✅ 精简预加载依赖配置

### 2. 插件优化
- ✅ 关闭图片压缩插件（可在CI/CD中启用）
- ✅ 添加并行处理插件
- ✅ 添加缓存优化插件
- ✅ 优化rollup配置

### 3. 依赖优化
- ✅ 外部化大型依赖
- ✅ 排除不必要的预构建依赖
- ✅ 优化chunk分割策略

## 🎯 使用方法

### 快速构建（推荐）
```bash
npm run build:fast
```

### 带包分析的构建
```bash
npm run build:analyze
```

### 传统构建
```bash
npm run build
```

## 📈 进一步优化建议

### 1. 开发环境优化
- 使用 `vite --host` 启动开发服务器
- 定期清理 `node_modules/.vite` 缓存
- 考虑使用 SWC 替代 esbuild（实验性）

### 2. CI/CD 优化
- 在CI环境中启用图片压缩
- 使用构建缓存
- 并行执行测试和构建

### 3. 代码层面优化
- 使用动态导入减少初始包大小
- 移除未使用的依赖
- 优化图片资源大小

## ⚠️ 注意事项

1. **图片压缩**: 已关闭以提升构建速度，如需要可在生产环境启用
2. **Sourcemap**: 生产环境已关闭，调试时可临时启用
3. **内存设置**: 确保 NODE_OPTIONS 设置足够的内存
4. **缓存**: 定期清理构建缓存以避免问题

## 🔧 故障排除

### 构建内存不足
```bash
export NODE_OPTIONS="--max-old-space-size=8192"
```

### 清理所有缓存
```bash
npm run clean:cache
```

### 重新安装依赖
```bash
npm run reinstall
```
