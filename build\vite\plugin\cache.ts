/**
 * 缓存优化插件 - 提升重复构建速度
 */
import type { PluginOption } from 'vite';
import path from 'path';

export function configCachePlugin(): PluginOption {
  return {
    name: 'vite-plugin-cache-optimization',
    config(config) {
      // 设置缓存目录
      const cacheDir = path.resolve(process.cwd(), 'node_modules/.vite');
      
      if (config.optimizeDeps) {
        config.optimizeDeps.cacheDir = cacheDir;
        // 启用强制缓存
        config.optimizeDeps.force = false;
      }
      
      // 设置构建缓存
      if (config.build) {
        config.build.rollupOptions = config.build.rollupOptions || {};
        config.build.rollupOptions.cache = true;
      }
    },
  };
}
