## 对应local环境

# 是否打开mock
VITE_USE_MOCK = true

# 发布路径
VITE_PUBLIC_PATH = /

# 是否启用gzip或brotli压缩
# 选项值: gzip | brotli | none
# 如果需要多个可以使用“,”分隔
VITE_BUILD_COMPRESS = 'gzip'

# 使用压缩时是否删除原始文件，默认为false
VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE = false

# 跨域代理，您可以配置多个 ,请注意，没有换行符
VITE_PROXY = [["/jeecgboot","http://************:9999"],["/upload","http://************:9999/upload"],["/baiduimg","https://api.map.baidu.com"]]

#后台接口父地址(必填)
VITE_GLOB_API_URL=/jeecgboot

#后台接口全路径地址(必填)
VITE_GLOB_DOMAIN_URL=http://************:9999

# 接口前缀
VITE_GLOB_API_URL_PREFIX=

# 是否启用图像压缩 (关闭以提升构建速度)
VITE_USE_IMAGEMIN= false

# 使用pwa
VITE_USE_PWA = false

# 是否兼容旧浏览器
VITE_LEGACY = false
